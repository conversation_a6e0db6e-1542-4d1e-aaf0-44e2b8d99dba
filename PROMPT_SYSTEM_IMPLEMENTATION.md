# Prompt Generation and Management System Implementation

## Overview

This implementation adds a comprehensive prompt generation and management system to the support ticket workflow. The system automatically generates structured prompts that combine all relevant information for GPT processing.

## ✅ Key Features Implemented

### 1. **Prompt Generation on Description Submission**
When a user submits their problem description, the backend automatically:
- ✅ Retrieves all product fields from the ticket
- ✅ Retrieves selected problem categories  
- ✅ Retrieves the user's full description
- ✅ Retrieves relevant document context chunks from vector DB
- ✅ Retrieves the full chat history for the ticket
- ✅ Combines everything into the specified final prompt format

### 2. **Final Prompt Format**
The generated prompt follows the exact format specified:

```
[PRODUCT DETAILS]
- Product: Camera
- Brand: <Brand>
- Sensor Type: <Sensor Type>
- Family: <Family Name>
- Model: <Model Number>
- Serial Number: <Serial Number>
- SDK: <SDK Name> (<SDK Version>)
- Programming Language: <Programming Language>
- Configuration Tool: <Camera Configuration Tool>
- Operating System: <Operating System>

[ISSUE CATEGORY]
- Category: <Comma-separated selected categories>

[USER'S DESCRIPTION]
"<Problem description>"

[CHAT HISTORY]
Full previous conversation with the user:
1. User: "<User message 1>"
   Bot: "<Bot reply 1>"
2. User: "<User message 2>"
   Bot: "<Bot reply 2>"
...

[DOCUMENT CONTEXT]
(Extracted chunks from uploaded user document or vector DB):
1. "<Matching chunk 1>"
2. "<Matching chunk 2>"
```

### 3. **Prompt Storage**
✅ **MySQL Storage**: The full generated prompt is saved in the `generated_prompt` column of the SupportTicket record.

✅ **JSON File Storage**: The same prompt is saved as a JSON file in `/support_prompts/` directory with format:
```json
{
  "ticket_number": "TCKT-XXXXXXXX",
  "prompt": "...",
  "last_updated": "2025-07-12T14:31:23.959895"
}
```

### 4. **Prompt Retrieval and Updates**
✅ **Future Access**: When accessing the same ticket, the system retrieves the saved prompt from MySQL instead of regenerating from scratch.

✅ **Dynamic Updates**: If chat history or description changes, the prompt is automatically updated and overwrites the old prompt in both MySQL and JSON file.

✅ **GPT Integration**: The updated prompt is used for every new GPT call tied to the ticket.

## 🔧 Technical Implementation

### New Database Model
```python
class ChatHistory(models.Model):
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='chat_messages')
    user_message = models.TextField()
    bot_response = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
```

### Key Functions Added

1. **`generate_final_prompt(ticket, user_description, context_chunks)`**
   - Generates the structured prompt according to specifications
   - Combines all ticket data, chat history, and document context

2. **`save_prompt_to_files(ticket, prompt)`**
   - Saves prompt to both MySQL and external JSON file
   - Creates `/support_prompts/` directory if needed

3. **`get_or_generate_ticket_prompt(ticket, user_description, context_chunks, force_regenerate)`**
   - Retrieves existing prompt or generates new one
   - Handles prompt updates when description/history changes

4. **`get_ticket_chat_history(ticket)`** & **`save_chat_message(ticket, user_message, bot_response)`**
   - Manages persistent chat history storage for tickets

### Updated Endpoints

1. **`/api/update_ticket_description/`**
   - Now generates initial prompt when description is submitted
   - Retrieves relevant document context automatically

2. **`/api/chat/`**
   - Uses ticket-specific prompts for ticket mode
   - Automatically saves chat exchanges to database
   - Updates prompts when new messages are added

3. **`/api/view_ticket_prompt/<ticket_number>/`** (New)
   - Allows viewing generated prompts for debugging/admin purposes

## 🚀 Workflow Integration

### Ticket Creation Flow
1. User creates ticket with product details
2. User selects problem categories
3. **User submits problem description** → **Prompt generation triggers**
4. System generates initial prompt with:
   - Product details
   - Selected categories  
   - Problem description
   - Relevant document context (from vector DB)
   - Empty chat history initially

### Chat Flow
1. User asks question in ticket chat
2. System retrieves existing prompt from MySQL
3. Adds current query to prompt
4. Sends to GPT for processing
5. Saves user question + GPT response to chat history
6. Updates prompt with new chat history
7. Overwrites prompt in MySQL and JSON file

## 📁 File Structure
```
/support_prompts/
├── TCKT-XXXXXXXX.json
├── TCKT-YYYYYYYY.json
└── ...
```

## ✅ Testing
The implementation has been tested with a comprehensive test script (`test_prompt_system.py`) that verifies:
- ✅ Prompt generation
- ✅ File storage (MySQL + JSON)
- ✅ Chat history management
- ✅ Prompt updates
- ✅ Prompt retrieval

## 🔄 Backward Compatibility
- ✅ Existing ticket status flow is preserved
- ✅ General chat mode (non-ticket) continues to work as before
- ✅ All existing functionality remains intact

## 🎯 Benefits
1. **Consistent Prompts**: Every GPT call uses the same structured format
2. **Complete Context**: All relevant information is included automatically
3. **Efficient Storage**: Prompts are cached to avoid regeneration
4. **Audit Trail**: JSON files provide external backup and audit capability
5. **Admin Visibility**: Admins can view and modify prompts externally
6. **Scalable**: System handles prompt updates efficiently as conversations grow

The implementation successfully meets all the specified requirements while maintaining the existing codebase functionality.
