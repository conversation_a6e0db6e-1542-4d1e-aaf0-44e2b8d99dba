from django.db import models
from django.contrib.auth.models import AbstractB<PERSON>User, BaseUserManager, PermissionsMixin
from django.utils import timezone


class PdfFile(models.Model):
    file_name = models.CharField(max_length=255, unique=True)
    file_data = models.BinaryField()
    last_modified = models.DateTimeField(null=True, blank=True) 
    keywords = models.JSONField(default=list, blank=True) # Added blank=True to allow empty form submissions if any

    class Meta:
        db_table = 'pdf_files'

    def __str__(self):
        return self.file_name


class CustomUserManager(BaseUserManager):
    def create_user(self, official_email, password=None, **extra_fields):
        if not official_email:
            raise ValueError("Users must have an official email")
        email = self.normalize_email(official_email)  # Normalize email before saving
        user = self.model(official_email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, official_email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        # Important: Validate superuser flags!
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(official_email, password, **extra_fields)


class CustomUser(AbstractBaseUser, PermissionsMixin):
    state = models.CharField(max_length=100)
    name = models.CharField(max_length=100)
    organization = models.CharField(max_length=200)
    address = models.TextField()
    official_email = models.EmailField(unique=True)
    alt_email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=20)
    mobile = models.CharField(max_length=20)

    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)

    objects = CustomUserManager()

    USERNAME_FIELD = 'official_email'
    REQUIRED_FIELDS = ['name', 'organization', 'phone', 'mobile']

    def save(self, *args, **kwargs):
        # Always normalize email before saving (good practice)
        self.official_email = self.__class__.objects.normalize_email(self.official_email)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.official_email
# chatbot/models.py
# chatbot/models.py
from django.db import models

from django.db import models

class PromptTemplate(models.Model):
    PROMPT_TYPE_CHOICES = [
        ("chat", "Chat Query Prompt"),
        ("img2", "GPT Vision Prompt"),
    ]

    name = models.CharField(max_length=100, unique=True, choices=PROMPT_TYPE_CHOICES)
    description = models.TextField()
    template = models.TextField()
    is_active = models.BooleanField(default=True)
    last_modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {'Active' if self.is_active else 'Inactive'}"


import string
import random
from django.db import models
from django.conf import settings

def generate_ticket_number():
    prefix = "TCKT"
    random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
    return f"{prefix}-{random_part}"

from django.db import models
from django.conf import settings
  

from django.db import models
from django.conf import settings
# from .utils import generate_ticket_number   # keep your existing import

class SupportTicket(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    ticket_number = models.CharField(max_length=20, unique=True, editable=False)
    purchased_from = models.CharField(max_length=255)
    year_of_purchase = models.CharField(max_length=10)

    # ── PRODUCT TYPE ────────────────────────────────────────────────────────────────
    PRODUCT_TYPE_CHOICES = [
        ("Camera", "Camera"),
        ("Frame Grabber", "Frame Grabber"),
        ("Accessories", "Accessories"),
        ("Software", "Software"),
    ]
    product_type = models.CharField(
        max_length=50,
        choices=PRODUCT_TYPE_CHOICES,
        default="Camera",
    )

    # ── CAMERA SPECIFIC FIELDS ─────────────────────────────────────────────────────
    BRAND_CHOICES = [
        ("DALSA", "DALSA"),
        ("FLIR", "FLIR"),
        ("Basler", "Basler"),
        ("Allied Vision", "Allied Vision"),
        ("Cognex", "Cognex"),
        ("Other", "Other"),
    ]
    brand = models.CharField(
        max_length=50,
        choices=BRAND_CHOICES,
        blank=True,
        null=True,
    )

    SENSOR_TYPE_CHOICES = [
        ("Area Scan", "Area Scan"),
        ("Line Scan", "Line Scan"),
    ]
    sensor_type = models.CharField(
        max_length=50,
        choices=SENSOR_TYPE_CHOICES,
        blank=True,
        null=True,
    )

    FAMILY_NAME_CHOICES = [
        ("Genie Nano", "Genie Nano"),
        ("Linea", "Linea"),
        ("Ace", "Ace"),
        ("Dart", "Dart"),
        ("Other", "Other"),
    ]
    family_name = models.CharField(
        max_length=100,
        choices=FAMILY_NAME_CHOICES,
        blank=True,
        null=True,
    )

    model_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="e.g., C1280M-25G"
    )

    serial_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="e.g., 12345678"
    )

    # ── SOFTWARE/SDK FIELDS ────────────────────────────────────────────────────────
    SDK_SOFTWARE_CHOICES = [
        ("Sapera LT", "Sapera LT"),
        ("Spinnaker", "Spinnaker"),
        ("Pylon", "Pylon"),
        ("VisionPoint", "VisionPoint"),
        ("Other", "Other"),
    ]
    sdk_software_used = models.CharField(
        max_length=100,
        choices=SDK_SOFTWARE_CHOICES,
        blank=True,
        null=True,
    )

    sdk_version = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="e.g., v6.10"
    )

    PROGRAMMING_LANGUAGE_CHOICES = [
        ("C#", "C#"),
        ("C++", "C++"),
        ("Python", "Python"),
        ("Java", "Java"),
        ("LabVIEW", "LabVIEW"),
        ("Other", "Other"),
    ]
    programming_language = models.CharField(
        max_length=50,
        choices=PROGRAMMING_LANGUAGE_CHOICES,
        blank=True,
        null=True,
    )

    CAMERA_CONFIG_TOOL_CHOICES = [
        ("IP Config Tool", "IP Config Tool"),
        ("eBUS Player", "eBUS Player"),
        ("Pylon Viewer", "Pylon Viewer"),
        ("CamExpert", "CamExpert"),
        ("Other", "Other"),
    ]
    camera_configuration_tool = models.CharField(
        max_length=100,
        choices=CAMERA_CONFIG_TOOL_CHOICES,
        blank=True,
        null=True,
    )

    OPERATING_SYSTEM_CHOICES = [
        ("Windows 10", "Windows 10"),
        ("Windows 11", "Windows 11"),
        ("Linux Ubuntu 20.04", "Linux Ubuntu 20.04"),
        ("Linux Ubuntu 22.04", "Linux Ubuntu 22.04"),
        ("CentOS", "CentOS"),
        ("Other", "Other"),
    ]
    operating_system_detailed = models.CharField(
        max_length=100,
        choices=OPERATING_SYSTEM_CHOICES,
        blank=True,
        null=True,
    )

    # ── EXISTING FIELDS ────────────────────────────────────────────────────────────
    product_name = models.CharField(max_length=255)
    model = models.CharField(max_length=255)
    serial_no = models.CharField(max_length=255)
    operating_system = models.CharField(max_length=255)
    po_number = models.CharField(max_length=255, blank=True, null=True)
    problem_description = models.TextField(blank=True, null=True)
    problem_summary = models.TextField(blank=True, null=True)
    solution_summary = models.TextField(blank=True, null=True)

    # ── ADDITIONAL FIELDS ──────────────────────────────────────────────────────────
    problem_categories = models.JSONField(
        default=list,
        blank=True,
        help_text="Selected problem categories"
    )
    generated_prompt = models.TextField(
        blank=True,
        null=True,
        help_text="Generated prompt for GPT"
    )
    prompt_file_path = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Path to external JSON prompt file"
    )

    TICKET_STATUS_CHOICES = [
        ("open", "Open"),
        ("closed", "Closed"),
        ("escalated", "Escalated"),
    ]
    status = models.CharField(
        max_length=10,
        choices=TICKET_STATUS_CHOICES,
        default="open",
    )
    short_title = models.CharField(max_length=120, blank=True)
    last_activity = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        # Generate ticket number if not set
        if not self.ticket_number:
            self.ticket_number = generate_ticket_number()

        # Set short_title if empty and product_name/model available
        if not self.short_title and self.product_name and self.model:
            self.short_title = f"{self.product_name} - {self.model}"

        # Update problem_summary from problem_description if empty
        if self.problem_description and not self.problem_summary:
            self.problem_summary = " ".join(
                self.problem_description.strip().split()[:20]
            )

        super().save(*args, **kwargs)

    def __str__(self):
        return f"Ticket {self.ticket_number} by {self.user}"


class OpenAIUsage(models.Model):
    """
    Model to track OpenAI API usage for cost monitoring and analytics.
    """
    API_TYPE_CHOICES = [
        ('chat_completion', 'Chat Completion'),
        ('embedding', 'Embedding'),
        ('other', 'Other'),
    ]

    model_name = models.CharField(
        max_length=100,
        help_text="OpenAI model used (e.g., gpt-4o-mini, text-embedding-ada-002)"
    )
    api_type = models.CharField(
        max_length=50,
        choices=API_TYPE_CHOICES,
        default='chat_completion'
    )
    prompt_tokens = models.IntegerField(
        default=0,
        help_text="Number of tokens in the prompt"
    )
    completion_tokens = models.IntegerField(
        default=0,
        help_text="Number of tokens in the completion"
    )
    total_tokens = models.IntegerField(
        default=0,
        help_text="Total tokens used"
    )
    cost_usd = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        help_text="Cost in USD"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who made the request"
    )
    request_timestamp = models.DateTimeField(
        default=timezone.now,
        help_text="When the API call was made"
    )
    response_time_ms = models.IntegerField(
        null=True,
        blank=True,
        help_text="Response time in milliseconds"
    )
    purpose = models.CharField(
        max_length=200,
        blank=True,
        help_text="Purpose of the API call (e.g., 'chat_response', 'embedding_generation')"
    )

    class Meta:
        db_table = 'openai_usage'
        ordering = ['-request_timestamp']
        indexes = [
            models.Index(fields=['request_timestamp']),
            models.Index(fields=['model_name']),
            models.Index(fields=['api_type']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return f"{self.model_name} - {self.api_type} - {self.request_timestamp}"


