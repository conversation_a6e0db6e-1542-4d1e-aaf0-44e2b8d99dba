import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ProblemCategoriesPage({ token }) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const ticketNumber = searchParams.get("ticket");
  const accessToken = token || localStorage.getItem("access");
  
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const problemCategories = [
    "Detection",
    "Acquisition & Triggering", 
    "Frame Rate Issue",
    "Software SDK Issues",
    "Focus & Exposure",
    "Other"
  ];

  useEffect(() => {
    if (!ticketNumber) {
      navigate("/actions");
    }
  }, [ticketNumber, navigate]);

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(c => c !== category);
      } else {
        return [...prev, category];
      }
    });
    if (error) setError("");
  };

  const handleOkayClick = async () => {
    if (selectedCategories.length === 0) {
      setError("Please select at least one problem category.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/update_ticket_categories/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          ticket_number: ticketNumber,
          problem_categories: selectedCategories,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to problem description page
        navigate(`/problem-description?ticket=${ticketNumber}`);
      } else {
        setError(data.message || "Failed to save categories. Please try again.");
      }
    } catch (err) {
      console.error("Error saving categories:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Problem Categories
      </h1>
      
      <p style={{
        fontSize: "1.1rem",
        color: "#666",
        marginBottom: "30px",
        textAlign: "center"
      }}>
        To help us assist you better, please select one or more problem categories that apply:
      </p>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <div style={{
        display: "flex",
        flexDirection: "column",
        gap: "15px",
        marginBottom: "30px"
      }}>
        {problemCategories.map((category) => (
          <label
            key={category}
            style={{
              display: "flex",
              alignItems: "center",
              padding: "15px",
              border: selectedCategories.includes(category) 
                ? "2px solid #4CAF50" 
                : "2px solid #ddd",
              borderRadius: "8px",
              cursor: "pointer",
              backgroundColor: selectedCategories.includes(category) 
                ? "#f8fff8" 
                : "white",
              transition: "all 0.2s ease"
            }}
          >
            <input
              type="checkbox"
              checked={selectedCategories.includes(category)}
              onChange={() => handleCategoryToggle(category)}
              style={{
                marginRight: "12px",
                width: "18px",
                height: "18px",
                cursor: "pointer"
              }}
            />
            <span style={{
              fontSize: "16px",
              fontWeight: selectedCategories.includes(category) ? "bold" : "normal",
              color: selectedCategories.includes(category) ? "#2e7d32" : "#333"
            }}>
              {category}
            </span>
          </label>
        ))}
      </div>

      <div style={{ textAlign: "center" }}>
        <button
          onClick={handleOkayClick}
          disabled={loading || selectedCategories.length === 0}
          style={{
            padding: "15px 40px",
            backgroundColor: loading || selectedCategories.length === 0 ? "#ccc" : "#4CAF50",
            color: "white",
            border: "none",
            borderRadius: "8px",
            fontSize: "18px",
            fontWeight: "bold",
            cursor: loading || selectedCategories.length === 0 ? "not-allowed" : "pointer",
            transition: "background-color 0.2s ease"
          }}
        >
          {loading ? "Saving..." : "Okay"}
        </button>
      </div>

      <div style={{ 
        textAlign: "center", 
        marginTop: "20px",
        fontSize: "14px",
        color: "#666"
      }}>
        Ticket #{ticketNumber}
      </div>
    </div>
  );
}
